import { SingleCompletionHandler } from "../index"

/**
 * Options for FIM (Fill in the Middle) API handler
 */
export interface FimHandlerOptions {
	apiKey: string
	baseUrl: string
	requestTimeoutMs?: number
	customHeaders?: Record<string, string>
}

/**
 * <PERSON>IM (Fill in the Middle) API handler for autocomplete
 * Supports the Fill in the Middle API format with prompt, suffix, and stream parameters
 */
export class <PERSON>m<PERSON><PERSON><PERSON> implements SingleCompletionHandler {
	private options: FimHandlerOptions

	constructor(options: FimHandlerOptions) {
		this.options = options
	}

	/**
	 * Complete a prompt using FIM API format
	 * @param prompt The code context before the cursor
	 * @param suffix The code context after the cursor (optional)
	 * @returns Promise<string> The completion text
	 */
	async completePrompt(prompt: string, suffix?: string): Promise<string> {
		const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`
		
		const headers: Record<string, string> = {
			"Content-Type": "application/json",
			"Authorization": `Bearer ${this.options.apiKey}`,
			...this.options.customHeaders,
		}

		const body = {
			prompt: prompt,
			suffix: suffix || "",
			stream: false, // For now, we'll use non-streaming
			max_tokens: 100, // Default max tokens for autocomplete
			temperature: 0.1, // Low temperature for more deterministic completions
		}

		try {
			const controller = new AbortController()
			const timeoutId = setTimeout(() => {
				controller.abort()
			}, this.options.requestTimeoutMs || 30000)

			const response = await fetch(url, {
				method: "POST",
				headers,
				body: JSON.stringify(body),
				signal: controller.signal,
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				const errorText = await response.text()
				throw new Error(`FIM API error: ${response.status} ${response.statusText} - ${errorText}`)
			}

			const data = await response.json()
			
			// Extract completion text from response
			// The exact format may vary depending on the FIM API implementation
			if (data.choices && data.choices.length > 0) {
				return data.choices[0].text || ""
			}
			
			// Fallback for different response formats
			if (data.completion) {
				return data.completion
			}
			
			if (data.text) {
				return data.text
			}

			return ""
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					throw new Error("FIM API request timed out")
				}
				throw new Error(`FIM API request failed: ${error.message}`)
			}
			throw new Error("FIM API request failed with unknown error")
		}
	}

	/**
	 * Complete a prompt with streaming support
	 * @param prompt The code context before the cursor
	 * @param suffix The code context after the cursor (optional)
	 * @returns AsyncGenerator<string> Stream of completion chunks
	 */
	async *completePromptStream(prompt: string, suffix?: string): AsyncGenerator<string> {
		const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`
		
		const headers: Record<string, string> = {
			"Content-Type": "application/json",
			"Authorization": `Bearer ${this.options.apiKey}`,
			...this.options.customHeaders,
		}

		const body = {
			prompt: prompt,
			suffix: suffix || "",
			stream: true,
			max_tokens: 100,
			temperature: 0.1,
		}

		try {
			const controller = new AbortController()
			const timeoutId = setTimeout(() => {
				controller.abort()
			}, this.options.requestTimeoutMs || 30000)

			const response = await fetch(url, {
				method: "POST",
				headers,
				body: JSON.stringify(body),
				signal: controller.signal,
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				const errorText = await response.text()
				throw new Error(`FIM API error: ${response.status} ${response.statusText} - ${errorText}`)
			}

			if (!response.body) {
				throw new Error("No response body for streaming request")
			}

			const reader = response.body.getReader()
			const decoder = new TextDecoder()

			try {
				while (true) {
					const { done, value } = await reader.read()
					if (done) break

					const chunk = decoder.decode(value, { stream: true })
					const lines = chunk.split('\n')

					for (const line of lines) {
						if (line.trim() === '') continue
						if (line.startsWith('data: ')) {
							const data = line.slice(6)
							if (data === '[DONE]') continue

							try {
								const parsed = JSON.parse(data)
								if (parsed.choices && parsed.choices.length > 0) {
									const text = parsed.choices[0].text || parsed.choices[0].delta?.text || ""
									if (text) {
										yield text
									}
								}
							} catch (e) {
								// Skip invalid JSON lines
								continue
							}
						}
					}
				}
			} finally {
				reader.releaseLock()
			}
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					throw new Error("FIM API streaming request timed out")
				}
				throw new Error(`FIM API streaming request failed: ${error.message}`)
			}
			throw new Error("FIM API streaming request failed with unknown error")
		}
	}
}
